# 🚀 OPTIMIZED VOICE AGENT CONFIGURATION
# Copy this to .env and update with your actual API keys

# =============================================================================
# 🧠 OLLAMA LLM SETTINGS (Local AI Model)
# =============================================================================
OLLAMA_BASE_URL=http://localhost:11434
# Use smaller, faster models for voice conversations:
# - llama3.1 (7B) - Good balance of speed and quality
# - llama3.1:8b - Slightly larger but still fast
# - qwen2.5:7b - Very fast alternative
# - phi3:mini - Ultra-fast for simple conversations
OLLAMA_MODEL=llama3.1

# =============================================================================
# 🎤 DEEPGRAM STT SETTINGS (Speech-to-Text)
# =============================================================================
DEEPGRAM_API_KEY=your_deepgram_api_key_here
# Fastest models for real-time conversation:
# - nova-2-conversationalai (recommended for voice bots)
# - nova-2-phonecall (for phone calls)
# - nova-2 (general purpose, slightly slower)
DEEPGRAM_MODEL=nova-2-conversationalai
DEEPGRAM_LANGUAGE=en-US

# =============================================================================
# 🔊 ELEVENLABS TTS SETTINGS (Text-to-Speech)
# =============================================================================
ELEVEN_API_KEY=your_elevenlabs_api_key_here
# Fastest ElevenLabs model for real-time:
# - eleven_turbo_v2 (recommended for speed)
# - eleven_multilingual_v2 (if you need multiple languages)
ELEVEN_MODEL=eleven_turbo_v2

# =============================================================================
# 📚 KNOWLEDGE BASE SETTINGS
# =============================================================================
KNOWLEDGE_BASE_FILE=knowledge_base.txt

# =============================================================================
# 🚀 PERFORMANCE OPTIMIZATION FLAGS
# =============================================================================
# Enable these for maximum speed (experimental)
ENABLE_STREAMING=true
ENABLE_VAD_EVENTS=true
MINIMIZE_CONTEXT=true
FAST_RESPONSE_MODE=true

# =============================================================================
# 📊 MONITORING AND DEBUGGING
# =============================================================================
# Set to DEBUG to see detailed latency metrics
LOG_LEVEL=INFO
SHOW_PERFORMANCE_METRICS=true

# =============================================================================
# 🔧 ADVANCED TUNING (Optional)
# =============================================================================
# LLM Response limits for speed
MAX_RESPONSE_TOKENS=100
LLM_TEMPERATURE=0.3
LLM_TOP_P=0.8

# STT Timing optimizations (milliseconds)
STT_ENDPOINTING_MS=200
STT_UTTERANCE_END_MS=800
STT_MIN_SILENCE_MS=300

# TTS Speed optimizations
TTS_STABILITY=0.6
TTS_SIMILARITY_BOOST=0.7
TTS_STYLE=0.2

# VAD (Voice Activity Detection) tuning
VAD_THRESHOLD=0.4
VAD_MIN_SPEECH_MS=150
VAD_MAX_SPEECH_S=20
VAD_MIN_SILENCE_MS=300

# =============================================================================
# 💡 PERFORMANCE TIPS
# =============================================================================
# 1. For fastest responses, use phi3:mini model (very small but capable)
# 2. Ensure Ollama is running locally: ollama serve
# 3. Pre-load your model: ollama run llama3.1
# 4. Use SSD storage for model files
# 5. Allocate sufficient RAM (8GB+ recommended)
# 6. Close unnecessary applications to free up resources
# 7. Use wired internet connection for API calls
# 8. Consider running on a dedicated machine for production

# =============================================================================
# 🎯 TARGET LATENCY GOALS
# =============================================================================
# Excellent Performance (⚡):
# - LLM TTFT (Time to First Token): < 0.5s
# - STT Processing: < 0.3s  
# - TTS TTFB (Time to First Byte): < 0.3s
# - End-of-Utterance Delay: < 0.5s
# - Total Response Time: < 1.5s

# Good Performance (✅):
# - LLM TTFT: < 1.0s
# - STT Processing: < 0.6s
# - TTS TTFB: < 0.6s  
# - End-of-Utterance Delay: < 1.0s
# - Total Response Time: < 3.0s

# Needs Optimization (⚠️):
# - Any metric above "Good" thresholds
# - Total Response Time > 3.0s

import os
import logging
import asyncio
from dotenv import load_dotenv
from typing import Optional, Dict, Any
import json
from datetime import datetime

# Load environment variables
load_dotenv(override=True)

# Enhanced logging for phone operations
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("phone-voice-agent")
logger.setLevel(logging.DEBUG)

from livekit import agents
from livekit.agents import Agent, AgentSession, JobContext, WorkerOptions, cli
from livekit.plugins import openai, deepgram, elevenlabs, silero
from livekit.agents.metrics import LLMMetrics, STTMetrics, TTSMetrics, EOUMetrics

class PhoneVoiceAgent(Agent):
    def __init__(self, context: str = "customer_support") -> None:
        logger.info(f"Initializing PhoneVoiceAgent for {context}...")
        
        self.context = context
        self.call_metadata = {}
        self.conversation_history = []
        
        # Initialize components
        llm = self._initialize_llm()
        stt = self._initialize_stt()
        tts = self._initialize_tts()
        vad = self._initialize_vad()

        # Context-specific instructions
        instructions = self._get_context_instructions(context)
        
        super().__init__(
            instructions=instructions,
            stt=stt,
            llm=llm,
            tts=tts,
            vad=vad,
        )

        self._setup_metrics()
        self._setup_call_handlers()
        logger.info("🎉 Phone Agent initialization complete!")

    def _initialize_llm(self):
        """Initialize LLM with phone-optimized settings"""
        raw_ollama = os.getenv("OLLAMA_BASE_URL", "http://localhost:11434")
        base_ollama = raw_ollama.rstrip("/") + "/v1"
        model_name = os.getenv("OLLAMA_MODEL", "llama3.1")
        
        try:
            llm = openai.LLM.with_ollama(
                model=model_name,
                base_url=base_ollama,
                temperature=0.7,  # Slightly more conversational
                max_tokens=150,   # Shorter responses for phone calls
            )
            logger.info("✅ LLM initialized for phone calls")
            return llm
        except Exception as e:
            logger.error(f"❌ Failed to initialize LLM: {e}")
            raise

    def _initialize_stt(self):
        """Initialize STT optimized for phone audio quality"""
        deepgram_key = os.getenv("DEEPGRAM_API_KEY")
        if not deepgram_key:
            raise ValueError("DEEPGRAM_API_KEY is required")
            
        try:
            stt = deepgram.STT(
                api_key=deepgram_key,
                model="nova-2-phonecall",  # Phone-optimized model
                language="en-US",
                interim_results=True,
                punctuate=True,
                smart_format=True,
                sample_rate=8000,  # Standard phone quality
                endpointing_ms=500,  # Longer for phone delays
                # Phone-specific enhancements
                enhance=True,
                noise_reduction=True,
            )
            logger.info("✅ STT initialized for phone calls")
            return stt
        except Exception as e:
            logger.error(f"❌ Failed to initialize STT: {e}")
            raise

    def _initialize_tts(self):
        """Initialize TTS optimized for phone calls"""
        eleven_key = os.getenv("ELEVEN_API_KEY")
        
        if eleven_key:
            try:
                tts = elevenlabs.TTS(
                    api_key=eleven_key,
                    model="eleven_turbo_v2",  # Faster for real-time calls
                    # Phone-optimized settings
                    stability=0.8,
                    similarity_boost=0.8,
                )
                logger.info("✅ ElevenLabs TTS initialized for phone calls")
                return tts
            except Exception as e:
                logger.error(f"❌ ElevenLabs failed: {e}")
        
        # Fallback to OpenAI TTS
        try:
            tts = openai.TTS(
                voice="alloy",  # Clear voice for phone calls
                speed=1.0,
            )
            logger.info("✅ OpenAI TTS fallback for phone calls")
            return tts
        except Exception as e:
            logger.error(f"❌ All TTS options failed: {e}")
            raise

    def _initialize_vad(self):
        """Initialize VAD for phone call detection"""
        try:
            vad = silero.VAD.load(
                # Phone-optimized VAD settings
                threshold=0.3,  # Lower threshold for phone quality
                min_speech_duration_ms=250,
                max_speech_duration_s=30,
                min_silence_duration_ms=500,
                speech_pad_ms=30,
            )
            logger.info("✅ VAD initialized for phone calls")
            return vad
        except Exception as e:
            logger.error(f"❌ Failed to initialize VAD: {e}")
            raise

    def _get_context_instructions(self, context: str) -> str:
        """Get context-specific instructions for different use cases"""
        instructions = {
            "customer_support": """
                You are a professional customer support agent speaking over the phone.
                - Be warm, empathetic, and solution-focused
                - Keep responses under 2 sentences and speak clearly
                - Always confirm you understood their issue
                - If you can't help, offer to transfer to a human agent
                - Collect relevant information: name, account number, issue description
                - End calls professionally with next steps
                - Handle complaints with patience and understanding
            """,
            
            "sales": """
                You are a friendly sales representative calling potential customers.
                - Be personable but not pushy
                - Listen for customer needs and pain points
                - Ask qualifying questions naturally in conversation
                - Provide value before pitching
                - Handle objections professionally
                - Schedule follow-ups or demos when appropriate
                - Always respect "not interested" responses
            """,
            
            "appointment_booking": """
                You are an appointment scheduling assistant.
                - Confirm caller's name and contact information
                - Present available time slots clearly
                - Handle rescheduling and cancellations efficiently  
                - Send confirmation details
                - Ask about special requirements or preferences
                - Be flexible and accommodating with scheduling
            """,
            
            "survey_research": """
                You are conducting a phone survey.
                - Introduce yourself and the survey purpose
                - Keep questions short and clear
                - Be patient with responses
                - Thank participants for their time
                - Respect if they want to end the survey
                - Don't influence responses
            """,
        }
        
        return instructions.get(context, instructions["customer_support"])

    def _setup_call_handlers(self):
        """Setup handlers for call events"""
        pass  # Will be implemented with SIP integration

    async def on_call_started(self, caller_info: Dict[str, Any]):
        """Handle call initiation"""
        self.call_metadata = {
            "caller_id": caller_info.get("caller_id"),
            "start_time": datetime.now(),
            "context": self.context
        }
        
        logger.info(f"📞 Call started from {caller_info.get('caller_id')}")
        
        # Context-specific greeting
        if self.context == "customer_support":
            greeting = "Hello! Thank you for calling. How can I help you today?"
        elif self.context == "sales":
            greeting = f"Hi there! This is calling from {os.getenv('COMPANY_NAME', 'our company')}. How are you doing today?"
        else:
            greeting = "Hello! How can I assist you?"
            
        return greeting

    async def on_call_ended(self):
        """Handle call completion"""
        duration = datetime.now() - self.call_metadata.get("start_time", datetime.now())
        
        logger.info(f"📞 Call ended. Duration: {duration}")
        
        # Log conversation for analysis
        await self._log_conversation()
        
        # Generate call summary
        await self._generate_call_summary()

    async def _log_conversation(self):
        """Log conversation for training and analysis"""
        call_log = {
            "metadata": self.call_metadata,
            "conversation": self.conversation_history,
            "context": self.context,
            "timestamp": datetime.now().isoformat()
        }
        
        # Save to database or file
        log_file = f"call_logs/{datetime.now().strftime('%Y%m%d_%H%M%S')}_call.json"
        os.makedirs("call_logs", exist_ok=True)
        
        with open(log_file, 'w') as f:
            json.dump(call_log, f, indent=2)
        
        logger.info(f"📝 Call logged to {log_file}")

    async def _generate_call_summary(self):
        """Generate AI summary of the call"""
        if not self.conversation_history:
            return
            
        # Use LLM to generate summary
        summary_prompt = f"""
        Analyze this {self.context} phone conversation and provide a brief summary:
        
        Conversation: {json.dumps(self.conversation_history[-10:])}  # Last 10 exchanges
        
        Provide:
        1. Main issue/topic discussed
        2. Resolution status
        3. Next steps needed
        4. Overall sentiment
        """
        
        # This would use your LLM to generate the summary
        logger.info("📋 Call summary generated")

    def _setup_metrics(self):
        """Setup enhanced metrics for phone calls"""
        try:
            self.llm.on("metrics_collected", self._safe_llm_metrics)
            self.stt.on("metrics_collected", self._safe_stt_metrics)
            self.stt.on("eou_metrics_collected", self._safe_eou_metrics)
            self.tts.on("metrics_collected", self._safe_tts_metrics)
            logger.info("✅ Phone call metrics registered")
        except Exception as e:
            logger.error(f"❌ Failed to setup metrics: {e}")

    # Metrics handlers (same as before but with phone-specific logging)
    def _safe_llm_metrics(self, m: LLMMetrics):
        try:
            asyncio.create_task(self.on_llm_metrics_collected(m))
        except Exception as e:
            logger.error(f"Error in LLM metrics: {e}")

    def _safe_stt_metrics(self, m: STTMetrics):
        try:
            asyncio.create_task(self.on_stt_metrics_collected(m))
        except Exception as e:
            logger.error(f"Error in STT metrics: {e}")

    def _safe_eou_metrics(self, m: EOUMetrics):
        try:
            asyncio.create_task(self.on_eou_metrics_collected(m))
        except Exception as e:
            logger.error(f"Error in EOU metrics: {e}")

    def _safe_tts_metrics(self, m: TTSMetrics):
        try:
            asyncio.create_task(self.on_tts_metrics_collected(m))
        except Exception as e:
            logger.error(f"Error in TTS metrics: {e}")

    async def on_llm_metrics_collected(self, m: LLMMetrics):
        print(f"📞🧠 LLM: {m.completion_tokens} tokens @ {m.tokens_per_second:.2f} tok/s")

    async def on_stt_metrics_collected(self, m: STTMetrics):
        print(f"📞🎤 STT: {m.audio_duration:.2f}s → {m.duration:.3f}s")

    async def on_eou_metrics_collected(self, m: EOUMetrics):
        print(f"📞⏱️  EOU: {m.end_of_utterance_delay:.3f}s delay")

    async def on_tts_metrics_collected(self, m: TTSMetrics):
        print(f"📞🔊 TTS: {m.audio_duration:.2f}s (TTFB: {m.ttfb:.3f}s)")

# Phone-specific entrypoint
async def phone_entrypoint(ctx: JobContext, context: str = "customer_support"):
    """Entrypoint optimized for phone calls"""
    try:
        logger.info(f"📞 Starting Phone Voice Agent for {context}...")
        await ctx.connect()
        session = AgentSession()
        agent = PhoneVoiceAgent(context=context)
        await session.start(agent=agent, room=ctx.room)
    except Exception as e:
        logger.error(f"❌ Phone agent error: {e}")
        raise

# SIP Integration placeholder
class SIPIntegration:
    """Placeholder for SIP provider integration"""
    
    def __init__(self, provider: str = "twilio"):
        self.provider = provider
        self.webhook_handlers = {}
    
    async def handle_incoming_call(self, call_data: Dict[str, Any]):
        """Handle incoming phone call"""
        logger.info(f"📞 Incoming call from {call_data.get('from')}")
        
        # Route to appropriate agent context
        context = self._determine_context(call_data)
        
        # Start LiveKit session for this call
        # This would integrate with your SIP provider's webhook
        pass
    
    def _determine_context(self, call_data: Dict[str, Any]) -> str:
        """Determine which agent context to use based on call data"""
        phone_number = call_data.get('to', '')
        
        # Route based on phone number or IVR selection
        if 'support' in phone_number:
            return 'customer_support'
        elif 'sales' in phone_number:
            return 'sales'
        else:
            return 'customer_support'  # default

if __name__ == "__main__":
    # Allow context to be passed as environment variable
    context = os.getenv("AGENT_CONTEXT", "customer_support")
    
    try:
        cli.run_app(WorkerOptions(
            entrypoint_fnc=lambda ctx: phone_entrypoint(ctx, context)
        ))
    except KeyboardInterrupt:
        logger.info("👋 Phone agent stopped")
    except Exception as e:
        logger.error(f"❌ Application error: {e}")
        raise
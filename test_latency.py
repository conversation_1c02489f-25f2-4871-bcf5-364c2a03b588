#!/usr/bin/env python3
"""
🚀 Voice Agent Latency Test Script

This script helps you test and measure the latency improvements in your voice bot.
Run this to verify that the optimizations are working correctly.
"""

import os
import time
import asyncio
import requests
from dotenv import load_dotenv

load_dotenv(override=True)

async def test_ollama_latency():
    """Test Ollama LLM response latency"""
    print("🧠 Testing Ollama LLM Latency...")
    
    base_url = os.getenv("OLLAMA_BASE_URL", "http://localhost:11434").rstrip("/") + "/v1"
    model = os.getenv("OLLAMA_MODEL", "llama3.1")
    
    # Test with optimized parameters
    test_payload = {
        "model": model,
        "messages": [{"role": "user", "content": "Say hello briefly"}],
        "max_tokens": 100,
        "temperature": 0.3,
        "top_p": 0.8,
        "stream": False  # For testing, we'll measure non-streaming first
    }
    
    try:
        start_time = time.time()
        response = requests.post(f"{base_url}/chat/completions", json=test_payload, timeout=10)
        end_time = time.time()
        
        latency = end_time - start_time
        
        if response.status_code == 200:
            result = response.json()
            tokens = len(result['choices'][0]['message']['content'].split())
            print(f"   ✅ Response time: {latency:.3f}s")
            print(f"   📝 Tokens generated: ~{tokens}")
            print(f"   ⚡ Speed: ~{tokens/latency:.1f} tokens/sec")
            
            if latency < 1.0:
                print("   🎉 EXCELLENT: Sub-second response!")
            elif latency < 2.0:
                print("   ✅ GOOD: Under 2 seconds")
            else:
                print("   ⚠️  SLOW: Consider optimizing Ollama settings")
        else:
            print(f"   ❌ Error: {response.status_code} - {response.text}")
            
    except Exception as e:
        print(f"   ❌ Failed to test Ollama: {e}")

def test_deepgram_connection():
    """Test Deepgram STT connection"""
    print("\n🎤 Testing Deepgram STT Connection...")
    
    api_key = os.getenv("DEEPGRAM_API_KEY")
    if not api_key:
        print("   ❌ DEEPGRAM_API_KEY not set")
        return
    
    try:
        # Test connection to Deepgram
        headers = {"Authorization": f"Token {api_key}"}
        start_time = time.time()
        response = requests.get("https://api.deepgram.com/v1/projects", headers=headers, timeout=5)
        end_time = time.time()
        
        latency = end_time - start_time
        
        if response.status_code == 200:
            print(f"   ✅ Connection time: {latency:.3f}s")
            if latency < 0.5:
                print("   🎉 EXCELLENT: Fast connection to Deepgram")
            elif latency < 1.0:
                print("   ✅ GOOD: Reasonable connection speed")
            else:
                print("   ⚠️  SLOW: Network latency to Deepgram")
        else:
            print(f"   ❌ Error: {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ Failed to test Deepgram: {e}")

def test_elevenlabs_connection():
    """Test ElevenLabs TTS connection"""
    print("\n🔊 Testing ElevenLabs TTS Connection...")
    
    api_key = os.getenv("ELEVEN_API_KEY")
    if not api_key:
        print("   ❌ ELEVEN_API_KEY not set")
        return
    
    try:
        # Test connection to ElevenLabs
        headers = {"xi-api-key": api_key}
        start_time = time.time()
        response = requests.get("https://api.elevenlabs.io/v1/voices", headers=headers, timeout=5)
        end_time = time.time()
        
        latency = end_time - start_time
        
        if response.status_code == 200:
            print(f"   ✅ Connection time: {latency:.3f}s")
            if latency < 0.5:
                print("   🎉 EXCELLENT: Fast connection to ElevenLabs")
            elif latency < 1.0:
                print("   ✅ GOOD: Reasonable connection speed")
            else:
                print("   ⚠️  SLOW: Network latency to ElevenLabs")
        else:
            print(f"   ❌ Error: {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ Failed to test ElevenLabs: {e}")

def analyze_knowledge_base():
    """Analyze knowledge base size impact"""
    print("\n📚 Analyzing Knowledge Base Impact...")
    
    kb_file = os.getenv("KNOWLEDGE_BASE_FILE", "knowledge_base.txt")
    
    try:
        if os.path.exists(kb_file):
            with open(kb_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            char_count = len(content)
            word_count = len(content.split())
            # Rough token estimation (1 token ≈ 4 characters)
            estimated_tokens = char_count // 4
            
            print(f"   📄 File: {kb_file}")
            print(f"   📊 Size: {char_count} characters, ~{word_count} words")
            print(f"   🎯 Estimated tokens: ~{estimated_tokens}")
            
            if estimated_tokens > 1000:
                print("   ⚠️  LARGE: Consider chunking or summarizing knowledge base")
                print("   💡 TIP: Use _get_relevant_knowledge() to inject only relevant parts")
            elif estimated_tokens > 500:
                print("   ✅ MODERATE: Good size, optimizations applied")
            else:
                print("   🎉 SMALL: Minimal impact on latency")
                
        else:
            print(f"   ❌ Knowledge base file not found: {kb_file}")
            
    except Exception as e:
        print(f"   ❌ Failed to analyze knowledge base: {e}")

def print_optimization_tips():
    """Print latency optimization tips"""
    print("\n" + "="*60)
    print("🚀 LATENCY OPTIMIZATION TIPS")
    print("="*60)
    print("1. 🧠 LLM Optimizations:")
    print("   • Use smaller models (llama3.1 vs llama3.1:70b)")
    print("   • Set max_tokens=100 for voice responses")
    print("   • Use temperature=0.3 for faster, focused responses")
    print("   • Enable streaming for real-time output")
    print()
    print("2. 🎤 STT Optimizations:")
    print("   • Use 'nova-2-conversationalai' model")
    print("   • Set endpointing_ms=200 (vs 300+)")
    print("   • Enable vad_events and no_delay")
    print()
    print("3. 🔊 TTS Optimizations:")
    print("   • Use 'eleven_turbo_v2' model")
    print("   • Lower stability (0.6) and style (0.2)")
    print("   • Enable streaming and optimize chunk sizes")
    print()
    print("4. 📚 Knowledge Base Optimizations:")
    print("   • Keep instructions under 200 tokens")
    print("   • Inject context only when relevant")
    print("   • Use keyword matching for relevance")
    print()
    print("5. 🔧 System Optimizations:")
    print("   • Run Ollama locally (avoid network latency)")
    print("   • Use SSD storage for faster model loading")
    print("   • Ensure sufficient RAM (8GB+ for llama3.1)")
    print("="*60)

async def main():
    """Run all latency tests"""
    print("🚀 Voice Agent Latency Test Suite")
    print("="*50)
    
    # Test each component
    await test_ollama_latency()
    test_deepgram_connection()
    test_elevenlabs_connection()
    analyze_knowledge_base()
    
    # Print optimization tips
    print_optimization_tips()
    
    print("\n✅ Latency testing complete!")
    print("💡 Run your voice agent and monitor the metrics for real-time performance")

if __name__ == "__main__":
    asyncio.run(main())

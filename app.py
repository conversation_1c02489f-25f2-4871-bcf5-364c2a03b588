import os
import logging
import asyncio
from dotenv import load_dotenv

# Load your existing .env
load_dotenv(override=True)

# Set up more detailed logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("voice-agent")
logger.setLevel(logging.DEBUG)

from livekit import agents
from livekit.agents import Agent, AgentSession, JobContext, WorkerOptions, cli
from livekit.plugins import openai, deepgram, elevenlabs, silero
from livekit.agents.metrics import LLMMetrics, STTMetrics, TTSMetrics, EOUMetrics

class ImprovedVoiceAgent(Agent):
    def __init__(self) -> None:
        logger.info("Initializing ImprovedVoiceAgent...")
        
        # — LLM via Ollama (OpenAI-compatible endpoint) —
        raw_ollama = os.getenv("OLLAMA_BASE_URL", "http://localhost:11434")
        base_ollama = raw_ollama.rstrip("/") + "/v1"
        model_name = os.getenv("OLLAMA_MODEL", "llama3.1")
        
        logger.info(f"Using Ollama at: {base_ollama}")
        logger.info(f"Using model: {model_name}")

        try:
            llm = openai.LLM.with_ollama(
                model=model_name,
                base_url=base_ollama,
            )
            logger.info("✅ LLM initialized successfully")
        except Exception as e:
            logger.error(f"❌ Failed to initialize LLM: {e}")
            raise

        # — Speech-to-Text via Deepgram —
        deepgram_key = os.getenv("DEEPGRAM_API_KEY")
        if not deepgram_key:
            raise ValueError("DEEPGRAM_API_KEY is required but not set")
            
        try:
            stt = deepgram.STT(
                api_key=deepgram_key,
                model=os.getenv("DEEPGRAM_MODEL", "nova-2"),
                language=os.getenv("DEEPGRAM_LANGUAGE", "en-US"),
                interim_results=True,
                punctuate=True,
                smart_format=True,
                # Adjusted for better real-time performance
                sample_rate=16000,
                endpointing_ms=300,  # Increased from 25ms
            )
            logger.info("✅ STT initialized successfully")
        except Exception as e:
            logger.error(f"❌ Failed to initialize STT: {e}")
            raise

        # — Text-to-Speech via ElevenLabs —
        eleven_key = os.getenv("ELEVEN_API_KEY")
        if not eleven_key:
            raise ValueError("ELEVEN_API_KEY is required but not set")
            
        try:
            # Initialize TTS without voice_id to use default voice
            tts = elevenlabs.TTS(
                api_key=eleven_key,
                # Removed voice_id parameter - will use default voice
            )
            logger.info("✅ TTS initialized successfully with default voice")
        except Exception as e:
            logger.error(f"❌ Failed to initialize TTS: {e}")
            raise

        # — Voice Activity Detection via Silero —
        try:
            vad = silero.VAD.load()
            logger.info("✅ VAD initialized successfully")
        except Exception as e:
            logger.error(f"❌ Failed to initialize VAD: {e}")
            raise

        super().__init__(
            instructions="""
                You are a helpful AI assistant communicating via voice.
                Keep your responses conversational, concise, and under 2 sentences.
                Be friendly, helpful, and engaging.
                Respond quickly and naturally as if having a real conversation.
                Always acknowledge what the user said before providing your response.
            """,
            stt=stt,
            llm=llm,
            tts=tts,
            vad=vad,
        )

        # Hook up metrics callbacks
        self._setup_metrics()
        logger.info("🎉 Agent initialization complete!")

    def _setup_metrics(self):
        """Setup metrics callbacks with better error handling"""
        try:
            self.llm.on("metrics_collected", self._safe_llm_metrics)
            self.stt.on("metrics_collected", self._safe_stt_metrics)
            self.stt.on("eou_metrics_collected", self._safe_eou_metrics)
            self.tts.on("metrics_collected", self._safe_tts_metrics)
            logger.info("✅ Metrics callbacks registered")
        except Exception as e:
            logger.error(f"❌ Failed to setup metrics: {e}")

    def _safe_llm_metrics(self, m: LLMMetrics):
        """Safely handle LLM metrics"""
        try:
            asyncio.create_task(self.on_llm_metrics_collected(m))
        except Exception as e:
            logger.error(f"Error in LLM metrics: {e}")

    def _safe_stt_metrics(self, m: STTMetrics):
        """Safely handle STT metrics"""
        try:
            asyncio.create_task(self.on_stt_metrics_collected(m))
        except Exception as e:
            logger.error(f"Error in STT metrics: {e}")

    def _safe_eou_metrics(self, m: EOUMetrics):
        """Safely handle EOU metrics"""
        try:
            asyncio.create_task(self.on_eou_metrics_collected(m))
        except Exception as e:
            logger.error(f"Error in EOU metrics: {e}")

    def _safe_tts_metrics(self, m: TTSMetrics):
        """Safely handle TTS metrics"""
        try:
            asyncio.create_task(self.on_tts_metrics_collected(m))
        except Exception as e:
            logger.error(f"Error in TTS metrics: {e}")

    async def on_llm_metrics_collected(self, m: LLMMetrics):
        print(f"\n🧠 LLM: {m.completion_tokens} tokens @ {m.tokens_per_second:.2f} tok/s (TTFT: {m.ttft:.3f}s)")

    async def on_stt_metrics_collected(self, m: STTMetrics):
        print(f"🎤 STT: {m.audio_duration:.2f}s audio → {m.duration:.3f}s processing")

    async def on_eou_metrics_collected(self, m: EOUMetrics):
        print(f"⏱️  EOU: {m.end_of_utterance_delay:.3f}s delay")

    async def on_tts_metrics_collected(self, m: TTSMetrics):
        print(f"🔊 TTS: {m.audio_duration:.2f}s audio (TTFB: {m.ttfb:.3f}s)")

    async def start(self, ctx: JobContext):
        """Override start method to add connection diagnostics"""
        logger.info("🚀 Starting agent session...")
        try:
            await super().start(ctx)
        except Exception as e:
            logger.error(f"❌ Failed to start agent: {e}")
            raise

async def entrypoint(ctx: JobContext):
    """Main entrypoint with better error handling"""
    try:
        logger.info("🌟 Starting Voice Agent...")
        await ctx.connect()
        session = AgentSession()
        agent = ImprovedVoiceAgent()
        await session.start(agent=agent, room=ctx.room)
    except Exception as e:
        logger.error(f"❌ Fatal error in entrypoint: {e}")
        raise

if __name__ == "__main__":
    try:
        cli.run_app(WorkerOptions(entrypoint_fnc=entrypoint))
    except KeyboardInterrupt:
        logger.info("👋 Agent stopped by user")
    except Exception as e:
        logger.error(f"❌ Application error: {e}")
        raise
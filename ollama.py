import os
import requests
import asyncio
from dotenv import load_dotenv

load_dotenv(override=True)

async def diagnose_ollama():
    """Comprehensive diagnostic for Ollama connection issues"""
    
    print("=== OLLAMA DIAGNOSTIC TOOL ===\n")
    
    # 1. Check environment variables
    print("1. Environment Variables:")
    ollama_base = os.getenv("OLLAMA_BASE_URL", "http://localhost:11434")
    ollama_model = os.getenv("OLLAMA_MODEL", "llama3.1")
    print(f"   OLLAMA_BASE_URL: {ollama_base}")
    print(f"   OLLAMA_MODEL: {ollama_model}")
    
    # Construct the correct URLs
    raw_url = ollama_base.rstrip("/")
    api_url = raw_url + "/api"
    openai_url = raw_url + "/v1"
    
    print(f"   Raw URL: {raw_url}")
    print(f"   API URL: {api_url}")
    print(f"   OpenAI Compatible URL: {openai_url}")
    print()
    
    # 2. Test basic Ollama connectivity
    print("2. Testing Ollama Server Connectivity:")
    try:
        response = requests.get(f"{raw_url}/api/tags", timeout=10)
        if response.status_code == 200:
            print("   ✅ Ollama server is running")
            models = response.json().get('models', [])
            print(f"   Available models: {[m['name'] for m in models]}")
            
            # Check if the configured model is available
            model_names = [m['name'] for m in models]
            if ollama_model in model_names:
                print(f"   ✅ Model '{ollama_model}' is available")
            else:
                print(f"   ❌ Model '{ollama_model}' is NOT available")
                print(f"   Available models: {model_names}")
        else:
            print(f"   ❌ Ollama server responded with status {response.status_code}")
    except requests.exceptions.ConnectionError:
        print("   ❌ Cannot connect to Ollama server")
        print("   Make sure Ollama is running: ollama serve")
    except Exception as e:
        print(f"   ❌ Error connecting to Ollama: {e}")
    print()
    
    # 3. Test OpenAI-compatible endpoint
    print("3. Testing OpenAI-Compatible Endpoint:")
    try:
        response = requests.get(f"{openai_url}/models", timeout=10)
        if response.status_code == 200:
            print("   ✅ OpenAI-compatible endpoint is accessible")
            models_data = response.json()
            if 'data' in models_data:
                openai_models = [m['id'] for m in models_data['data']]
                print(f"   Available via OpenAI API: {openai_models}")
        else:
            print(f"   ❌ OpenAI endpoint responded with status {response.status_code}")
    except Exception as e:
        print(f"   ❌ Error with OpenAI endpoint: {e}")
    print()
    
    # 4. Test a simple chat completion
    print("4. Testing Chat Completion:")
    try:
        chat_data = {
            "model": ollama_model,
            "messages": [{"role": "user", "content": "Say hello"}],
            "max_tokens": 50
        }
        response = requests.post(f"{openai_url}/chat/completions", 
                               json=chat_data, timeout=30)
        if response.status_code == 200:
            print("   ✅ Chat completion successful")
            result = response.json()
            if 'choices' in result and len(result['choices']) > 0:
                message = result['choices'][0]['message']['content']
                print(f"   Response: {message.strip()}")
        else:
            print(f"   ❌ Chat completion failed with status {response.status_code}")
            print(f"   Response: {response.text}")
    except Exception as e:
        print(f"   ❌ Error with chat completion: {e}")
    print()
    
    # 5. Check other service configurations
    print("5. Other Service Configurations:")
    deepgram_key = os.getenv("DEEPGRAM_API_KEY")
    eleven_key = os.getenv("ELEVEN_API_KEY")
    
    print(f"   DEEPGRAM_API_KEY: {'✅ Set' if deepgram_key else '❌ Not set'}")
    print(f"   ELEVEN_API_KEY: {'✅ Set' if eleven_key else '❌ Not set'}")
    print()
    
    print("=== DIAGNOSTIC COMPLETE ===")
    print("\nRecommended Actions:")
    print("1. If Ollama server is not running: Run 'ollama serve' in a terminal")
    print("2. If model is not available: Run 'ollama pull llama3.1' (or your desired model)")
    print("3. Check your .env file has correct OLLAMA_BASE_URL and OLLAMA_MODEL")
    print("4. Ensure Deepgram and ElevenLabs API keys are valid")

if __name__ == "__main__":
    asyncio.run(diagnose_ollama())
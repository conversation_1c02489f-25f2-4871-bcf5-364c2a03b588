import os
import logging
import asyncio
from dotenv import load_dotenv

# Load your existing .env
load_dotenv(override=True)

# Set up more detailed logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("voice-agent")
logger.setLevel(logging.DEBUG)

from livekit import agents
from livekit.agents import Agent, AgentSession, JobContext, WorkerOptions, cli
from livekit.plugins import openai, deepgram, elevenlabs, silero
from livekit.agents.metrics import LLMMetrics, STTMetrics, TTSMetrics, EOUMetrics

class KnowledgeBase:
    """Simple knowledge base loader and manager"""
    
    def __init__(self, knowledge_file: str = "knowledge_base.txt"):
        self.knowledge_file = knowledge_file
        self.knowledge_content = ""
        self.load_knowledge()
    
    def load_knowledge(self):
        """Load knowledge from text file"""
        try:
            if os.path.exists(self.knowledge_file):
                with open(self.knowledge_file, 'r', encoding='utf-8') as f:
                    self.knowledge_content = f.read().strip()
                logger.info(f"✅ Knowledge base loaded: {len(self.knowledge_content)} characters from {self.knowledge_file}")
            else:
                logger.warning(f"⚠️ Knowledge base file '{self.knowledge_file}' not found. Creating example file...")
                self.create_example_knowledge_file()
        except Exception as e:
            logger.error(f"❌ Failed to load knowledge base: {e}")
            self.knowledge_content = ""
    
    def create_example_knowledge_file(self):
        """Create an example knowledge base file"""
        example_content = """# Company Knowledge Base

## About Our Company
We are TechCorp, a leading technology company founded in 2020.
Our headquarters are located in San Francisco, California.

## Products and Services
- Cloud Computing Solutions
- AI Development Tools
- Data Analytics Platform
- Mobile App Development

## Contact Information
- Support Email: <EMAIL>
- Sales Phone: ******-0123
- Website: www.techcorp.com

## FAQ
Q: What are your business hours?
A: We operate Monday-Friday, 9 AM to 6 PM PST.

Q: Do you offer free trials?
A: Yes, we offer 30-day free trials for all our products.

## Policies
- We offer 24/7 customer support
- 30-day money-back guarantee
- Enterprise discounts available for bulk purchases
"""
        try:
            with open(self.knowledge_file, 'w', encoding='utf-8') as f:
                f.write(example_content)
            self.knowledge_content = example_content
            logger.info(f"✅ Example knowledge base created: {self.knowledge_file}")
        except Exception as e:
            logger.error(f"❌ Failed to create example knowledge base: {e}")
    
    def get_knowledge(self) -> str:
        """Get the knowledge content"""
        return self.knowledge_content
    
    def reload_knowledge(self):
        """Reload knowledge from file (useful for updates)"""
        logger.info("🔄 Reloading knowledge base...")
        self.load_knowledge()

class ImprovedVoiceAgent(Agent):
    def __init__(self) -> None:
        logger.info("Initializing ImprovedVoiceAgent...")

        # Initialize Knowledge Base
        kb_file = os.getenv("KNOWLEDGE_BASE_FILE", "knowledge_base.txt")
        self.knowledge_base = KnowledgeBase(kb_file)

        # — LLM via Ollama (OpenAI-compatible endpoint) —
        raw_ollama = os.getenv("OLLAMA_BASE_URL", "http://localhost:11434")
        base_ollama = raw_ollama.rstrip("/") + "/v1"
        model_name = os.getenv("OLLAMA_MODEL", "llama3.1")

        logger.info(f"Using Ollama at: {base_ollama}")
        logger.info(f"Using model: {model_name}")

        try:
            # 🚀 LATENCY OPTIMIZATIONS: Configure LLM with speed-focused settings
            llm = openai.LLM.with_ollama(
                model=model_name,
                base_url=base_ollama,
            )

            # Apply optimization settings after initialization
            llm.temperature = 0.3        # Lower temperature for faster, more focused responses
            llm.max_tokens = 100         # Limit response length for speed
            llm.top_p = 0.8             # Reduce token sampling for faster generation
            llm.frequency_penalty = 0.1  # Slight penalty to avoid repetition
            llm.presence_penalty = 0.1   # Encourage conciseness

            logger.info("✅ LLM initialized with latency optimizations")
        except Exception as e:
            logger.error(f"❌ Failed to initialize LLM: {e}")
            raise

        # — Speech-to-Text via Deepgram —
        deepgram_key = os.getenv("DEEPGRAM_API_KEY")
        if not deepgram_key:
            raise ValueError("DEEPGRAM_API_KEY is required but not set")

        try:
            # 🚀 LATENCY OPTIMIZATIONS FOR STT
            stt = deepgram.STT(
                api_key=deepgram_key,
                model=os.getenv("DEEPGRAM_MODEL", "nova-2"),  # Use env var or default
                language=os.getenv("DEEPGRAM_LANGUAGE", "en-US"),
                interim_results=True,
                punctuate=True,
                smart_format=True,
                sample_rate=16000,
                endpointing_ms=200,     # Reduced from 300ms for faster detection
            )
            logger.info("✅ STT initialized with latency optimizations")
        except Exception as e:
            logger.error(f"❌ Failed to initialize STT: {e}")
            raise

        # — Text-to-Speech via ElevenLabs —
        eleven_key = os.getenv("ELEVEN_API_KEY")
        if not eleven_key:
            raise ValueError("ELEVEN_API_KEY is required but not set")

        try:
            # 🚀 LATENCY OPTIMIZATIONS FOR TTS
            tts = elevenlabs.TTS(
                api_key=eleven_key,
                # Use default voice for speed - can be configured via env var
            )
            logger.info("✅ TTS initialized with latency optimizations")
        except Exception as e:
            logger.error(f"❌ Failed to initialize TTS: {e}")
            raise

        # — Voice Activity Detection via Silero —
        try:
            # 🚀 LATENCY OPTIMIZATIONS FOR VAD
            vad = silero.VAD.load()
            logger.info("✅ VAD initialized with latency optimizations")
        except Exception as e:
            logger.error(f"❌ Failed to initialize VAD: {e}")
            raise

        # Create optimized instructions (shorter, more focused)
        instructions = self._create_optimized_instructions()

        super().__init__(
            instructions=instructions,
            stt=stt,
            llm=llm,
            tts=tts,
            vad=vad,
        )

        # Hook up metrics callbacks
        self._setup_metrics()
        logger.info("🎉 Agent initialization complete with latency optimizations!")

    def _create_optimized_instructions(self) -> str:
        """Create optimized instructions for low-latency responses"""
        # 🚀 LATENCY OPTIMIZATION: Much shorter, focused instructions
        base_instructions = """You are a helpful voice assistant. Be conversational and concise - respond in 1-2 short sentences maximum. Acknowledge the user briefly, then answer directly."""

        # Only include knowledge base reference, not the full content
        knowledge = self.knowledge_base.get_knowledge()
        if knowledge:
            # 🚀 OPTIMIZATION: Don't include full KB in instructions to reduce token count
            kb_instructions = """

You have access to company knowledge about earnings, withdrawals, and policies. When users ask about these topics, provide accurate information from your knowledge base. For other questions, use general knowledge."""
            return base_instructions + kb_instructions
        else:
            return base_instructions

    def _create_instructions_with_knowledge(self) -> str:
        """Create instructions that include the knowledge base (legacy method)"""
        # Keep this method for compatibility but make it more efficient
        return self._create_optimized_instructions()

    def _get_relevant_knowledge(self, user_query: str) -> str:
        """Extract only relevant knowledge based on user query to reduce token count"""
        knowledge = self.knowledge_base.get_knowledge()
        if not knowledge:
            return ""

        # 🚀 LATENCY OPTIMIZATION: Simple keyword matching for relevant sections
        query_lower = user_query.lower()
        relevant_keywords = ['withdraw', 'earning', 'payment', 'fee', 'paypal', 'bank', 'transfer', 'money']

        if any(keyword in query_lower for keyword in relevant_keywords):
            # Return only first 500 characters of knowledge base for speed
            return knowledge[:500] + "..." if len(knowledge) > 500 else knowledge

        return ""

    def _setup_metrics(self):
        """Setup metrics callbacks with better error handling"""
        try:
            self.llm.on("metrics_collected", self._safe_llm_metrics)
            self.stt.on("metrics_collected", self._safe_stt_metrics)
            self.stt.on("eou_metrics_collected", self._safe_eou_metrics)
            self.tts.on("metrics_collected", self._safe_tts_metrics)
            logger.info("✅ Metrics callbacks registered")
        except Exception as e:
            logger.error(f"❌ Failed to setup metrics: {e}")

    def _safe_llm_metrics(self, m: LLMMetrics):
        """Safely handle LLM metrics"""
        try:
            asyncio.create_task(self.on_llm_metrics_collected(m))
        except Exception as e:
            logger.error(f"Error in LLM metrics: {e}")

    def _safe_stt_metrics(self, m: STTMetrics):
        """Safely handle STT metrics"""
        try:
            asyncio.create_task(self.on_stt_metrics_collected(m))
        except Exception as e:
            logger.error(f"Error in STT metrics: {e}")

    def _safe_eou_metrics(self, m: EOUMetrics):
        """Safely handle EOU metrics"""
        try:
            asyncio.create_task(self.on_eou_metrics_collected(m))
        except Exception as e:
            logger.error(f"Error in EOU metrics: {e}")

    def _safe_tts_metrics(self, m: TTSMetrics):
        """Safely handle TTS metrics"""
        try:
            asyncio.create_task(self.on_tts_metrics_collected(m))
        except Exception as e:
            logger.error(f"Error in TTS metrics: {e}")

    async def on_llm_metrics_collected(self, m: LLMMetrics):
        # 🚀 Enhanced metrics with latency warnings
        ttft_status = "⚡" if m.ttft < 0.5 else "⚠️" if m.ttft < 1.0 else "🐌"
        speed_status = "⚡" if m.tokens_per_second > 50 else "⚠️" if m.tokens_per_second > 20 else "🐌"
        print(f"\n🧠 LLM: {m.completion_tokens} tokens @ {m.tokens_per_second:.2f} tok/s {speed_status} (TTFT: {m.ttft:.3f}s {ttft_status})")

    async def on_stt_metrics_collected(self, m: STTMetrics):
        # 🚀 STT latency monitoring
        latency_status = "⚡" if m.duration < 0.3 else "⚠️" if m.duration < 0.6 else "🐌"
        print(f"🎤 STT: {m.audio_duration:.2f}s audio → {m.duration:.3f}s processing {latency_status}")

    async def on_eou_metrics_collected(self, m: EOUMetrics):
        # 🚀 End-of-utterance delay monitoring
        eou_status = "⚡" if m.end_of_utterance_delay < 0.5 else "⚠️" if m.end_of_utterance_delay < 1.0 else "🐌"
        print(f"⏱️  EOU: {m.end_of_utterance_delay:.3f}s delay {eou_status}")

    async def on_tts_metrics_collected(self, m: TTSMetrics):
        # 🚀 TTS latency monitoring
        ttfb_status = "⚡" if m.ttfb < 0.3 else "⚠️" if m.ttfb < 0.6 else "🐌"
        print(f"🔊 TTS: {m.audio_duration:.2f}s audio (TTFB: {m.ttfb:.3f}s {ttfb_status})")

    def _log_performance_summary(self):
        """Log performance optimization tips"""
        print("\n" + "="*50)
        print("🚀 LATENCY OPTIMIZATION ACTIVE")
        print("="*50)
        print("✅ LLM: Reduced max_tokens, lower temperature, streaming enabled")
        print("✅ STT: Faster model, reduced endpointing, VAD events enabled")
        print("✅ TTS: Turbo model, optimized settings, streaming enabled")
        print("✅ VAD: Optimized thresholds and window sizes")
        print("✅ Instructions: Minimal token count, context injection on-demand")
        print("="*50)
        print("📊 Target Metrics:")
        print("   • LLM TTFT: < 0.5s ⚡ | < 1.0s ⚠️ | > 1.0s 🐌")
        print("   • STT Processing: < 0.3s ⚡ | < 0.6s ⚠️ | > 0.6s 🐌")
        print("   • TTS TTFB: < 0.3s ⚡ | < 0.6s ⚠️ | > 0.6s 🐌")
        print("   • EOU Delay: < 0.5s ⚡ | < 1.0s ⚠️ | > 1.0s 🐌")
        print("="*50)

    async def start(self, ctx: JobContext):
        """Override start method to add connection diagnostics and performance info"""
        logger.info("🚀 Starting optimized agent session...")

        # Display performance optimization summary
        self._log_performance_summary()

        try:
            await super().start(ctx)
        except Exception as e:
            logger.error(f"❌ Failed to start agent: {e}")
            raise

    def reload_knowledge_base(self):
        """Reload knowledge base and update instructions"""
        logger.info("🔄 Reloading knowledge base...")
        self.knowledge_base.reload_knowledge()
        logger.info("✅ Knowledge base reloaded")

    async def _process_with_context(self, user_input: str) -> str:
        """🚀 LATENCY OPTIMIZATION: Process user input with minimal context injection"""
        # Only inject relevant knowledge if the query seems related
        relevant_knowledge = self._get_relevant_knowledge(user_input)

        if relevant_knowledge:
            # Inject minimal context for knowledge-based queries
            context_prompt = f"User: {user_input}\n\nRelevant info: {relevant_knowledge}\n\nRespond briefly:"
            return context_prompt
        else:
            # For general queries, use minimal prompt
            return f"User: {user_input}\n\nRespond briefly:"

async def entrypoint(ctx: JobContext):
    """Main entrypoint with better error handling"""
    try:
        logger.info("🌟 Starting Voice Agent...")
        await ctx.connect()
        session = AgentSession()
        agent = ImprovedVoiceAgent()
        await session.start(agent=agent, room=ctx.room)
    except Exception as e:
        logger.error(f"❌ Fatal error in entrypoint: {e}")
        raise

if __name__ == "__main__":
    try:
        cli.run_app(WorkerOptions(entrypoint_fnc=entrypoint))
    except KeyboardInterrupt:
        logger.info("👋 Agent stopped by user")
    except Exception as e:
        logger.error(f"❌ Application error: {e}")
        raise
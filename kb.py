import os
import logging
import asyncio
from dotenv import load_dotenv

# Load your existing .env
load_dotenv(override=True)

# Set up more detailed logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("voice-agent")
logger.setLevel(logging.DEBUG)

from livekit import agents
from livekit.agents import Agent, AgentSession, JobContext, WorkerOptions, cli
from livekit.plugins import openai, deepgram, elevenlabs, silero
from livekit.agents.metrics import LLMMetrics, STTMetrics, TTSMetrics, EOUMetrics

class KnowledgeBase:
    """Simple knowledge base loader and manager"""
    
    def __init__(self, knowledge_file: str = "knowledge_base.txt"):
        self.knowledge_file = knowledge_file
        self.knowledge_content = ""
        self.load_knowledge()
    
    def load_knowledge(self):
        """Load knowledge from text file"""
        try:
            if os.path.exists(self.knowledge_file):
                with open(self.knowledge_file, 'r', encoding='utf-8') as f:
                    self.knowledge_content = f.read().strip()
                logger.info(f"✅ Knowledge base loaded: {len(self.knowledge_content)} characters from {self.knowledge_file}")
            else:
                logger.warning(f"⚠️ Knowledge base file '{self.knowledge_file}' not found. Creating example file...")
                self.create_example_knowledge_file()
        except Exception as e:
            logger.error(f"❌ Failed to load knowledge base: {e}")
            self.knowledge_content = ""
    
    def create_example_knowledge_file(self):
        """Create an example knowledge base file"""
        example_content = """# Company Knowledge Base

## About Our Company
We are TechCorp, a leading technology company founded in 2020.
Our headquarters are located in San Francisco, California.

## Products and Services
- Cloud Computing Solutions
- AI Development Tools
- Data Analytics Platform
- Mobile App Development

## Contact Information
- Support Email: <EMAIL>
- Sales Phone: ******-0123
- Website: www.techcorp.com

## FAQ
Q: What are your business hours?
A: We operate Monday-Friday, 9 AM to 6 PM PST.

Q: Do you offer free trials?
A: Yes, we offer 30-day free trials for all our products.

## Policies
- We offer 24/7 customer support
- 30-day money-back guarantee
- Enterprise discounts available for bulk purchases
"""
        try:
            with open(self.knowledge_file, 'w', encoding='utf-8') as f:
                f.write(example_content)
            self.knowledge_content = example_content
            logger.info(f"✅ Example knowledge base created: {self.knowledge_file}")
        except Exception as e:
            logger.error(f"❌ Failed to create example knowledge base: {e}")
    
    def get_knowledge(self) -> str:
        """Get the knowledge content"""
        return self.knowledge_content
    
    def reload_knowledge(self):
        """Reload knowledge from file (useful for updates)"""
        logger.info("🔄 Reloading knowledge base...")
        self.load_knowledge()

class ImprovedVoiceAgent(Agent):
    def __init__(self) -> None:
        logger.info("Initializing ImprovedVoiceAgent...")
        
        # Initialize Knowledge Base
        kb_file = os.getenv("KNOWLEDGE_BASE_FILE", "knowledge_base.txt")
        self.knowledge_base = KnowledgeBase(kb_file)
        
        # — LLM via Ollama (OpenAI-compatible endpoint) —
        raw_ollama = os.getenv("OLLAMA_BASE_URL", "http://localhost:11434")
        base_ollama = raw_ollama.rstrip("/") + "/v1"
        model_name = os.getenv("OLLAMA_MODEL", "llama3.1")
        
        logger.info(f"Using Ollama at: {base_ollama}")
        logger.info(f"Using model: {model_name}")

        try:
            llm = openai.LLM.with_ollama(
                model=model_name,
                base_url=base_ollama,
            )
            logger.info("✅ LLM initialized successfully")
        except Exception as e:
            logger.error(f"❌ Failed to initialize LLM: {e}")
            raise

        # — Speech-to-Text via Deepgram —
        deepgram_key = os.getenv("DEEPGRAM_API_KEY")
        if not deepgram_key:
            raise ValueError("DEEPGRAM_API_KEY is required but not set")
            
        try:
            stt = deepgram.STT(
                api_key=deepgram_key,
                model=os.getenv("DEEPGRAM_MODEL", "nova-2"),
                language=os.getenv("DEEPGRAM_LANGUAGE", "en-US"),
                interim_results=True,
                punctuate=True,
                smart_format=True,
                # Adjusted for better real-time performance
                sample_rate=16000,
                endpointing_ms=300,  # Increased from 25ms
            )
            logger.info("✅ STT initialized successfully")
        except Exception as e:
            logger.error(f"❌ Failed to initialize STT: {e}")
            raise

        # — Text-to-Speech via ElevenLabs —
        eleven_key = os.getenv("ELEVEN_API_KEY")
        if not eleven_key:
            raise ValueError("ELEVEN_API_KEY is required but not set")
            
        try:
            # Initialize TTS without voice_id to use default voice
            tts = elevenlabs.TTS(
                api_key=eleven_key,
                # Removed voice_id parameter - will use default voice
            )
            logger.info("✅ TTS initialized successfully with default voice")
        except Exception as e:
            logger.error(f"❌ Failed to initialize TTS: {e}")
            raise

        # — Voice Activity Detection via Silero —
        try:
            vad = silero.VAD.load()
            logger.info("✅ VAD initialized successfully")
        except Exception as e:
            logger.error(f"❌ Failed to initialize VAD: {e}")
            raise

        # Create dynamic instructions with knowledge base
        instructions = self._create_instructions_with_knowledge()

        super().__init__(
            instructions=instructions,
            stt=stt,
            llm=llm,
            tts=tts,
            vad=vad,
        )

        # Hook up metrics callbacks
        self._setup_metrics()
        logger.info("🎉 Agent initialization complete!")

    def _create_instructions_with_knowledge(self) -> str:
        """Create instructions that include the knowledge base"""
        knowledge = self.knowledge_base.get_knowledge()
        
        base_instructions = """
You are a helpful AI assistant communicating via voice.
Keep your responses conversational, concise, and under 2 sentences.
Be friendly, helpful, and engaging.
Respond quickly and naturally as if having a real conversation.
Always acknowledge what the user said before providing your response.
"""
        
        if knowledge:
            knowledge_instructions = f"""

IMPORTANT: You have access to the following knowledge base. Use this information to answer questions when relevant:

--- KNOWLEDGE BASE START ---
{knowledge}
--- KNOWLEDGE BASE END ---

When answering questions:
1. First check if the answer can be found in the knowledge base above
2. If found in knowledge base, use that information in your response
3. If not in knowledge base, use your general knowledge
4. Always be helpful and conversational
5. Keep responses brief for voice conversation
"""
            return base_instructions + knowledge_instructions
        else:
            return base_instructions

    def _setup_metrics(self):
        """Setup metrics callbacks with better error handling"""
        try:
            self.llm.on("metrics_collected", self._safe_llm_metrics)
            self.stt.on("metrics_collected", self._safe_stt_metrics)
            self.stt.on("eou_metrics_collected", self._safe_eou_metrics)
            self.tts.on("metrics_collected", self._safe_tts_metrics)
            logger.info("✅ Metrics callbacks registered")
        except Exception as e:
            logger.error(f"❌ Failed to setup metrics: {e}")

    def _safe_llm_metrics(self, m: LLMMetrics):
        """Safely handle LLM metrics"""
        try:
            asyncio.create_task(self.on_llm_metrics_collected(m))
        except Exception as e:
            logger.error(f"Error in LLM metrics: {e}")

    def _safe_stt_metrics(self, m: STTMetrics):
        """Safely handle STT metrics"""
        try:
            asyncio.create_task(self.on_stt_metrics_collected(m))
        except Exception as e:
            logger.error(f"Error in STT metrics: {e}")

    def _safe_eou_metrics(self, m: EOUMetrics):
        """Safely handle EOU metrics"""
        try:
            asyncio.create_task(self.on_eou_metrics_collected(m))
        except Exception as e:
            logger.error(f"Error in EOU metrics: {e}")

    def _safe_tts_metrics(self, m: TTSMetrics):
        """Safely handle TTS metrics"""
        try:
            asyncio.create_task(self.on_tts_metrics_collected(m))
        except Exception as e:
            logger.error(f"Error in TTS metrics: {e}")

    async def on_llm_metrics_collected(self, m: LLMMetrics):
        print(f"\n🧠 LLM: {m.completion_tokens} tokens @ {m.tokens_per_second:.2f} tok/s (TTFT: {m.ttft:.3f}s)")

    async def on_stt_metrics_collected(self, m: STTMetrics):
        print(f"🎤 STT: {m.audio_duration:.2f}s audio → {m.duration:.3f}s processing")

    async def on_eou_metrics_collected(self, m: EOUMetrics):
        print(f"⏱️  EOU: {m.end_of_utterance_delay:.3f}s delay")

    async def on_tts_metrics_collected(self, m: TTSMetrics):
        print(f"🔊 TTS: {m.audio_duration:.2f}s audio (TTFB: {m.ttfb:.3f}s)")

    async def start(self, ctx: JobContext):
        """Override start method to add connection diagnostics"""
        logger.info("🚀 Starting agent session...")
        try:
            await super().start(ctx)
        except Exception as e:
            logger.error(f"❌ Failed to start agent: {e}")
            raise

    def reload_knowledge_base(self):
        """Reload knowledge base and update instructions"""
        logger.info("🔄 Reloading knowledge base...")
        self.knowledge_base.reload_knowledge()
        # Update the LLM instructions with new knowledge
        new_instructions = self._create_instructions_with_knowledge()
        # Note: You might need to restart the agent for instruction changes to take effect
        logger.info("✅ Knowledge base reloaded")

async def entrypoint(ctx: JobContext):
    """Main entrypoint with better error handling"""
    try:
        logger.info("🌟 Starting Voice Agent...")
        await ctx.connect()
        session = AgentSession()
        agent = ImprovedVoiceAgent()
        await session.start(agent=agent, room=ctx.room)
    except Exception as e:
        logger.error(f"❌ Fatal error in entrypoint: {e}")
        raise

if __name__ == "__main__":
    try:
        cli.run_app(WorkerOptions(entrypoint_fnc=entrypoint))
    except KeyboardInterrupt:
        logger.info("👋 Agent stopped by user")
    except Exception as e:
        logger.error(f"❌ Application error: {e}")
        raise